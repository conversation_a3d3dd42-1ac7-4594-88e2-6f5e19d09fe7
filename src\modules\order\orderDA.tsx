import {de} from 'date-fns/locale';
import {DataController} from '../../base/baseController';

export class OrderDA {
  private orderController: DataController;
  private orderDetailController: DataController;
  private productController: DataController;
  private historyRewardController: DataController;

  constructor() {
    this.orderController = new DataController('Order');
    this.orderDetailController = new DataController('OrderDetail');
    this.productController = new DataController('Product');
    this.historyRewardController = new DataController('HistoryReward');
  }

  async getAllHistoryReward() {
    try {
      const response = await this.historyRewardController.getListSimple({
        query: `@Filial:[0 1 2]`,
      });

      if (response?.code === 200) {
        return response;
      }

      // Log error for debugging
      console.error('getAllHistoryReward failed:', response);
      return {
        code: response?.code || 500,
        data: [],
        message: response?.message || 'Failed to get history rewards',
      };
    } catch (error) {
      console.error('getAllHistoryReward error:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }

  async createOrder(order: any) {
    const response = await this.orderController.add(order);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async createOrderDetail(orderDetail: any) {
    const response = await this.orderDetailController.add(orderDetail);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  // async getOrderByShopId(ShopId: string, status: number) {
  //   // const response = await this.orderController.getPatternList({
  //   //   query: `@ShopId: {${ShopId}} @Status:[${status}]`,
  //   //   pattern: {
  //   //     ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
  //   //   },
  //   // });
  //   // if (response?.code === 200) {
  //   //   console.log('check-response-order', response?.data);
  //   //   let arrayData: any[] = [];
  //   //   for (let item of response?.data) {
  //   //     arrayData.push(item?.Id);
  //   //   }
  //   //   if (arrayData?.length > 0) {
  //   //     console.log('check-arrayData', arrayData);
  //   //     let response = await this.orderDetailController.getListSimple({
  //   //       query: `@OrderId: {${arrayData.join('|')}}`,
  //   //     });
  //   //     console.log('check-response-detail', response);
  //   //     if (response?.code === 200) {
  //   //       return response;
  //   //     }
  //   //   }
  //   //   return {
  //   //     code: 200,
  //   //     data: [],
  //   //     message: 'No orders found',
  //   //   };
  //   // }
  //   // return {
  //   //   code: 200,
  //   //   data: [],
  //   //   message: 'No orders found',
  //   // };
  async getOrderByShopId(ShopId: string, status: number) {
    return await this.orderController
      .getPatternList({
        query: `@ShopId: {${ShopId}} @Status:[${status} ${status}]`,
        pattern: {
          ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
        },
      })
      .then((response: any) => {
        if (response?.code === 200) {
          console.log('check-getOrderByShopId 1646', response);
          return response;
        }
        return null;
      });
  }

  async getAllOrdersByShopId(ShopId: string) {
    // Bước 1: Lấy tất cả orders từ bảng Order theo ShopId
    const orderResponse = await this.orderController.getPatternList({
      query: `@ShopId: {${ShopId}}`,
      pattern: {
        Id: [
          'Id',
          'Status',
          'CustomerId',
          'ShopId',
          'AddressId',
          'TotalAmount',
          'CreatedDate',
        ],
      },
    });

    if (
      orderResponse?.code !== 200 ||
      !orderResponse.data ||
      orderResponse.data.length === 0
    ) {
      return {
        code: 200,
        data: [],
        message: 'No orders found',
      };
    }
    // Lấy tất cả OrderId từ response
    const orderIds = orderResponse.data.map((order: any) => order.Id);
    // Bước 2: Gọi 1 lần API để lấy tất cả orderDetails
    const orderDetailResponse = await this.orderDetailController.getPatternList(
      {
        query: `@OrderId: {${orderIds.join('|')}}`,
        pattern: {
          OrderId: [
            'Id',
            'Status',
            'CustomerId',
            'ShopId',
            'AddressId',
            'TotalAmount',
            'CreatedDate',
          ],
          ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
        },
      },
    );

    if (orderDetailResponse?.code !== 200) {
      return {
        code: 200,
        data: [],
        message: 'Failed to get order details',
      };
    }
    // Bước 3: Lấy tất cả ProductId từ orderDetails
    const productIds = orderDetailResponse.data
      .map((orderDetail: any) => orderDetail.ProductId)
      .filter(Boolean);
    // Bước 4: Gọi 1 lần API để lấy thông tin tất cả products
    let productMap = new Map();
    if (productIds.length > 0) {
      const productResponse = await this.productController.getPatternList({
        query: `@Id: {${productIds.join('|')}}`,
        pattern: {
          Id: [
            'Id',
            'Name',
            'Img',
            'Description',
            'Price',
            'CategoryId',
            'ShopId',
          ],
        },
      });

      if (productResponse?.code === 200 && productResponse.data) {
        productResponse.data.forEach((product: any) => {
          productMap.set(product.Id, product);
        });
      }
    }
    // Bước 5: Gọi 1 lần API để lấy tất cả historyReward với điều kiện Filial là 0, 1, 2
    const orderDetailIds = orderDetailResponse.data.map(
      (orderDetail: any) => orderDetail.Id,
    );
    const historyRewardResponse =
      await this.historyRewardController.getPatternList({
        query: `@OrderDetailId: {${orderDetailIds.join('|')}} @Filial:[0 2]`,
        pattern: {
          OrderDetailId: ['Id', 'Money', 'Filial', 'Type'],
        },
      });
    // Bước 6: Tạo map cho historyReward data
    const historyRewardMap = new Map();
    if (historyRewardResponse?.code === 200 && historyRewardResponse.data) {
      historyRewardResponse.data.forEach((history: any) => {
        if (!historyRewardMap.has(history.OrderId)) {
          historyRewardMap.set(history.OrderId, []);
        }
        historyRewardMap.get(history.OrderId).push(history);
      });
    }
    // Bước 7: Map tất cả thông tin lại với nhau
    const result = orderDetailResponse.data.map((orderDetail: any) => {
      // Gắn thông tin order
      const orderInfo = orderResponse.data.find(
        (order: any) => order.Id === orderDetail.OrderId,
      );
      if (orderInfo) {
        orderDetail.orderInfo = orderInfo;
      }
      // Gắn thông tin product
      if (orderDetail.ProductId && productMap.has(orderDetail.ProductId)) {
        orderDetail.productInfo = productMap.get(orderDetail.ProductId);
      }
      // Gắn thông tin historyReward
      orderDetail.historyRewards =
        historyRewardMap.get(orderDetail.OrderId) || [];
      return orderDetail;
    });
    return {
      code: 200,
      data: result,
      message: 'Success',
    };
  }

  async getOrderByCustomerId(CustomerId: string) {
    const response = await this.orderController.getListSimple({
      query: `@CustomerId: {${CustomerId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getOrderByOrderId(OrderId: string) {
    const response = await this.orderController.getPatternList({
      query: `@Id: {${OrderId}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
        ShopId: ['Id', 'Name', 'Avatar'],
        AddressId: ['Id', 'Address'],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getOrderDetailsByOrderId(OrderId: string) {
    const response = await this.orderDetailController.getPatternList({
      query: `@OrderId: {${OrderId}}`,
      pattern: {
        ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getMoneyDetailsByOrderDetailId(OrderDetailId: string) {
    const response = await this.historyRewardController.getPatternList({
      query: `@OrderDetailId: {${OrderDetailId}} @Type :[1 1]`,
      pattern: {
        OrderDetailId: ['Id', 'Money'],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getInfoProductById(productId: string) {
    console.log('check-productId', productId);
    const response = await this.productController.getListSimple({
      query: `@Id: {${productId}}`,
    });
    if (response?.code === 200) {
      console.log('check-response-product', response);
      return response;
    }
    return null;
  }

  // async getOrderWithDetails(OrderId: string) {
  //   try {
  //     // Lấy thông tin đơn hàng
  //     const orderResponse = await this.getOrderByOrderId(OrderId);
  //     if (orderResponse?.code !== 200 || !orderResponse.data?.length) {
  //       return null;
  //     }

  //     const order = orderResponse.data[0];

  //     // Lấy chi tiết đơn hàng
  //     const detailsResponse = await this.getOrderDetailsByOrderId(OrderId);
  //     if (detailsResponse?.code === 200 && detailsResponse.data?.length) {
  //       // Lấy thông tin sản phẩm cho mỗi chi tiết đơn hàng
  //       const details = detailsResponse.data;
  //       const productIds = details.map(detail => detail.ProductId);

  //       // Lấy thông tin sản phẩm
  //       if (productIds.length > 0) {
  //         const productsQuery = productIds.map(id => `{${id}}`).join(' | ');
  //         const productsResponse = await this.productController.getListSimple({
  //           query: `@Id: ${productsQuery}`,
  //         });

  //         if (productsResponse?.code === 200 && productsResponse.data?.length) {
  //           const products = productsResponse.data;

  //           // Gắn thông tin sản phẩm vào chi tiết đơn hàng
  //           details.forEach(detail => {
  //             const product = products.find(p => p.Id === detail.ProductId);
  //             if (product) {
  //               detail.Product = product;
  //             }
  //           });
  //         }
  //       }

  //       order.details = details;
  //     }

  //     return order;
  //   } catch (error) {
  //     console.error('Error getting order with details:', error);
  //     return null;
  //   }
  // }
  //update trạng thái
  async updateStatusOrder(OrderId: string, Status: number) {
    //lấy order
    const order = await this.getOrderByOrderId(OrderId);
    if (order?.code === 200 && order?.data?.length > 0) {
      const response = await this.orderController.edit([
        {...order?.data[0], Status: Status},
      ]);
      if (response?.code === 200) {
        return response;
      }
    }
    return null;
  }

  async cancelOrder(OrderId: string) {
    return await this.updateStatusOrder(OrderId, 4);
  }
}
