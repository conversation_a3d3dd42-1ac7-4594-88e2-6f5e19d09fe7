# H<PERSON> thống Rank trong PointHome

## Tổng quan
Hệ thống rank được tích hợp vào màn hình PointHome để hiển thị hạng của người dùng dựa trên các điều kiện:
1. **Score**: Tổng điểm từ hoa hồng và nhiệm vụ
2. **OrderCount**: <PERSON><PERSON> đơn hàng thành công
3. **ReferralCount**: Số người được mời

## Cấu trúc ConfigRank
Bảng `ConfigRank` chứa thông tin các hạng với các trường:
- `Id`: ID của hạng
- `Name`: <PERSON><PERSON><PERSON> hạng (VD: "Đồng", "Bạc", "Vàng")
- `Score`: Điểm số yêu cầu để đạt hạng
- `Icon`: Icon củ<PERSON> hạ<PERSON> (URL)
- `OrderCount`: <PERSON>ố đơn hàng thành công yêu cầ<PERSON> (tù<PERSON> chọn)
- `ReferralCount`: <PERSON><PERSON> người mời yêu cầ<PERSON> (t<PERSON><PERSON> chọ<PERSON>)
- `Condition`: <PERSON>ô tả điều kiện (HTML)

## Logic tính toán Rank

### 1. Tính tổng điểm (Score)
```typescript
// Lấy tổng điểm từ HistoryReward với điều kiện:
// - Type = TransactionType.hoahong (hoa hồng) hoặc TransactionType.mission (nhiệm vụ)
// - Status = TransactionStatus.success (thành công)
const resSum = await controller.group({
    reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
    searchRaw: `@CustomerId: {${customer.Id}} (@Type: [${TransactionType.hoahong} ${TransactionType.hoahong}] | @Type: [${TransactionType.mission} ${TransactionType.mission}]) @Status: [${TransactionStatus.success}]`,
});
```

### 2. Đếm đơn hàng thành công
```typescript
// Đếm số đơn hàng có Status = StatusOrder.success
const resOrder = await orderController.getListSimple({
    query: `@CustomerId: {${customer.Id}} @Status: [${StatusOrder.success}]`,
    returns: ['Id']
});
```

### 3. Đếm số người được mời
```typescript
// Đếm số Customer có ParentId = customer.Id
const resReferral = await customerController.getListSimple({
    query: `@ParentId: {${customer.Id}}`,
    returns: ['Id']
});
```

### 4. Xác định hạng hiện tại
Hệ thống sẽ:
1. Sắp xếp các hạng theo điểm số tăng dần
2. Duyệt từng hạng và kiểm tra điều kiện:
   - Điểm số >= Score yêu cầu
   - Số đơn hàng >= OrderCount yêu cầu (nếu có)
   - Số người mời >= ReferralCount yêu cầu (nếu có)
3. Chọn hạng cao nhất mà người dùng đạt được

## Hiển thị UI

### Thông tin hiển thị:
1. **Tổng điểm**: Hiển thị với icon vàng
2. **Hạng hiện tại**: 
   - Nếu có hạng: Hiển thị icon hạng + tên hạng
   - Nếu chưa có hạng: Hiển thị "Chưa có hạng"
3. **Thống kê**: Số đơn hàng và số người mời (góc phải)

### Code hiển thị:
```tsx
<View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
    {currentRank ? (
        <>
            {currentRank.Icon ? (
                <FastImage
                    source={{ uri: ConfigAPI.urlImg + currentRank.Icon }}
                    style={{ width: 20, height: 20 }}
                    resizeMode="contain"
                />
            ) : (
                <AppSvg SvgSrc={iconSvg.ruby} size={20} />
            )}
            <Text>Hạng {currentRank.Name}</Text>
        </>
    ) : (
        <>
            <AppSvg SvgSrc={iconSvg.ruby} size={20} />
            <Text>Chưa có hạng</Text>
        </>
    )}
</View>
```

## Cách sử dụng

1. **Cấu hình hạng**: Thêm dữ liệu vào bảng `ConfigRank`
2. **Tự động cập nhật**: Hạng sẽ được tính toán lại mỗi khi component load
3. **Hiển thị real-time**: Thông tin được cập nhật theo dữ liệu thực tế

## Ví dụ dữ liệu ConfigRank

```json
[
    {
        "Id": "rank1",
        "Name": "Đồng",
        "Score": "0",
        "Icon": "bronze_icon.png",
        "OrderCount": 0,
        "ReferralCount": 0
    },
    {
        "Id": "rank2", 
        "Name": "Bạc",
        "Score": "1000",
        "Icon": "silver_icon.png",
        "OrderCount": 5,
        "ReferralCount": 2
    },
    {
        "Id": "rank3",
        "Name": "Vàng", 
        "Score": "5000",
        "Icon": "gold_icon.png",
        "OrderCount": 20,
        "ReferralCount": 10
    }
]
```

## Lưu ý
- Hệ thống sẽ chọn hạng cao nhất mà người dùng đạt được
- Tất cả điều kiện phải được thỏa mãn để đạt hạng
- Icon hạng được load từ ConfigAPI.urlImg
- Nếu không có icon, sẽ sử dụng icon mặc định
