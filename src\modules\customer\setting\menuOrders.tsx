import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import {AppSvg, Winicon} from 'wini-mobile-components';
import {Text} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {StatusOrder, Title} from '../../../Config/Contanst';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

interface MenuOrderProps {
  svgIcon: string;
  title: string;
  getBadgeOrder?: number;
  orderRoute: string;
  status?: number;
}
const MenuOrders = (props: MenuOrderProps) => {
  let {svgIcon, title, getBadgeOrder: getNewOrder, orderRoute, status} = props;
  const navigation = useNavigation<any>();
  const handleNavigateOrders = (order: string, status?: number) => {
    navigation.navigate(order, {
      status: status,
    });
  };
  return (
    <TouchableOpacity
      style={styles.navItem}
      onPress={() => handleNavigateOrders(orderRoute, status)}>
      <AppSvg SvgSrc={svgIcon} size={20} />
      <Text style={styles.navText} numberOfLines={2}>
        {title}
      </Text>
      {status == StatusOrder.success ? (
        <View></View>
      ) : (
        <Text style={styles.navBadge}>{getNewOrder}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#00FFFF',
    borderWidth: 0.3,
    padding: 10,
    borderRadius: 10,
    gap: 8,
    width: Dimensions.get('window').width / 5 - 16,
    height: 75,
  },
  navText: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  navBadge: {
    position: 'absolute',
    top: 5,
    right: 4,
    backgroundColor: '#FF0000',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 4,
    borderRadius: 10,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  cardHeaderLeft: {
    display: 'flex',
    flexDirection: 'row',
    alignContent: 'center',
    gap: 3,
    flex: 1,
  },
  cardHeaderRight: {},

  cardContent: {
    marginTop: 10,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
  },
  infoValueEdit: {
    lineHeight: 14,
    display: 'flex',
    alignItems: 'center',
    color: '#000',
    fontWeight: '500',
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    height: 70,
  },
  actionText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    marginLeft: 10,
  },

  input: {
    flex: 1,
    fontSize: 16,
    color: 'black',
    borderWidth: 0,
    backgroundColor: 'white',
    lineHeight: 14,
  },
});

export default MenuOrders;
