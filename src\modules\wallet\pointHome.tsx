import { ImageBackground, Text, View } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { AppSvg } from "wini-mobile-components";
import iconSvg from "../../svg/icon";
import { TypoSkin } from "../../assets/skin/typography";
import { ColorThemes } from "../../assets/skin/colors";
import { useSelectorCustomerState } from "../../redux/hook/customerHook";
import { useEffect, useState } from "react";
import { DataController } from "../../base/baseController";
import { TransactionType, TransactionStatus, StatusOrder } from "../../Config/Contanst";
import ConfigAPI from "../../Config/ConfigAPI";
import FastImage from "react-native-fast-image";

const PointHome = () => {
    const customer = useSelectorCustomerState().data;
    const [totalReward, setTotalReward] = useState(0);
    const [currentRank, setCurrentRank] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [orderCount, setOrderCount] = useState(0);
    const [referralCount, setReferralCount] = useState(0);

    const fetchData = async () => {
        if (!customer?.Id) return;

        setLoading(true);

        // 1. Tính tổng điểm từ hoa hồng và nhiệm vụ
        const controller = new DataController('HistoryReward');
        const totalRewardRes = await controller.group({
            reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
            searchRaw: `@CustomerId: {${customer.Id}} @Status: [${TransactionStatus.pending},${TransactionStatus.success}] `,
        });
        if (totalRewardRes.code === 200 && totalRewardRes.data.length > 0) {
            setTotalReward(totalRewardRes.data[0].TotalReward || 0);
        }
        const resSum = await controller.group({
            reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
            searchRaw: `@CustomerId: {${customer.Id}} (@Type: [${TransactionType.hoahong} ${TransactionType.hoahong}] | @Type: [${TransactionType.mission} ${TransactionType.mission}]) @Status: [${TransactionStatus.success}]`,
        });

        let totalScore = 0;
        if (resSum.code === 200 && resSum.data.length > 0) {
            totalScore = resSum.data[0].TotalReward || 0;            
        }

        // 2. Đếm số đơn hàng thành công
        const orderController = new DataController('Order');
        const resOrder = await orderController.group({
            reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE COUNT 1 @Id AS OrderCount',
            searchRaw: `@CustomerId: {${customer.Id}} @Status: [${StatusOrder.success}]`,
        });
        const successfulOrders = resOrder.code === 200 ? resOrder.data.length : 0;
        setOrderCount(successfulOrders);

        // 3. Đếm số người được mời (referral)
        const customerController = new DataController('Customer');
        const resReferral = await customerController.getListSimple({
            query: `@ParentId: {${customer.Id}}`,
            returns: ['Id']
        });
        const referrals = resReferral.code === 200 ? resReferral.data.length : 0;
        setReferralCount(referrals);

        // 4. Lấy thông tin config rank và xác định rank hiện tại
        const rankController = new DataController('ConfigRank');
        const resRank = await rankController.getAll();

        if (resRank.code === 200 && resRank.data.length > 0) {
            const ranksData = resRank.data;

            // Sắp xếp ranks theo điểm số tăng dần
            const sortedRanks = [...ranksData].sort(
                (a, b) => parseFloat(a.Score) - parseFloat(b.Score)
            );

            // Tìm rank hiện tại dựa trên điều kiện
            let achievedRank = null;

            for (const rank of sortedRanks) {
                const requiredScore = parseFloat(rank.Score);

                // Kiểm tra điều kiện điểm số
                if (totalScore >= requiredScore) {
                    // Kiểm tra điều kiện đơn hàng (nếu có)
                    const orderCondition = rank.OrderCount || 0;
                    if (successfulOrders >= orderCondition) {
                        // Kiểm tra điều kiện referral (nếu có)
                        const referralCondition = rank.ReferralCount || 0;
                        if (referrals >= referralCondition) {
                            achievedRank = rank;
                        }
                    }
                }
            }

            setCurrentRank(achievedRank);
        }

        setLoading(false);
    };
    useEffect(() => {
        if (customer?.Id) {
            fetchData();
        }
    }, [customer?.Id]);
    return (
        <View style={{
            height: 82, backgroundColor: 'white',
            marginHorizontal: 16,
            marginTop: 16,
            position: 'relative',
            borderRadius: 10,
            overflow: 'hidden',
        }}>

            <LinearGradient
                colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
                start={{ x: 0, y: 0.5 }}
                end={{ x: 1, y: 0.5 }}
                style={{
                    borderRadius: 10,
                    height: '100%',
                    width: '100%',
                    overflow: 'hidden',
                }}
            >
                <ImageBackground
                    source={require('../../assets/bg04.png')}
                    resizeMode="contain"
                    style={{
                        position: 'absolute',
                        bottom: -30,
                        left: -20,
                        width: 110,
                        height: 110,
                    }}
                />
                <View style={{ paddingLeft: 76, paddingTop: 16 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                        <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
                        <Text style={{ ...TypoSkin.heading7, color: ColorThemes.light.neutral_text_title_color }}>
                            {totalReward} điểm
                        </Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                        {currentRank ? (
                            <>
                                {currentRank.Icon ? (
                                    <FastImage
                                        source={{ uri: ConfigAPI.urlImg + currentRank.Icon }}
                                        style={{ width: 20, height: 20 }}
                                        resizeMode="contain"
                                    />
                                ) : (
                                    <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                                )}
                                <Text style={{ ...TypoSkin.heading7, color: ColorThemes.light.neutral_text_title_color }}>
                                    Hạng {currentRank.Name}
                                </Text>
                            </>
                        ) : (
                            <>
                                <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                                <Text style={{ ...TypoSkin.heading7, color: ColorThemes.light.neutral_text_title_color,fontWeight: '500' }}>
                                    Chưa có hạng
                                </Text>
                            </>
                        )}
                    </View>
                </View>
                <View style={{ position: 'absolute', right: 16, top: 16 }}>
                    <LinearGradient
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF']}
                        style={{ paddingHorizontal: 16, paddingVertical: 8, borderRadius: 8 }}>
                        <Text style={{ ...TypoSkin.body2, color: ColorThemes.light.primary_main_color,fontSize: 14,fontWeight: '500' }}>
                            Đổi quà
                        </Text>
                    </LinearGradient>
                </View>
            </LinearGradient>
        </View>
    );
};

export default PointHome;
