import { ImageBackground, Text, View } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { AppSvg } from "wini-mobile-components";
import iconSvg from "../../svg/icon";
import { TypoSkin } from "../../assets/skin/typography";
import { ColorThemes } from "../../assets/skin/colors";
import { useSelectorCustomerState } from "../../redux/hook/customerHook";
import { useEffect, useState } from "react";
import { DataController } from "../../base/baseController";

const PointHome = () => {
    const customer = useSelectorCustomerState().data;
    const [totalReward, setTotalReward] = useState(0);
    const [loading, setLoading] = useState(false);
    const fetchData = async () => {
        setLoading(true);
        // Gọi API để lấy dữ liệu ví
        const controller = new DataController('HistoryReward');
        //sum total reward
        const resSum = await controller.group({
            reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
            searchRaw: `@CustomerId: {${customer.Id}}`,
        });
        if (resSum.code === 200) {
            setTotalReward(resSum.data[0].TotalReward);
        }
        setLoading(false);
    };
    useEffect(() => {
        fetchData();
    }, []);
    return (
        <View style={{
            height: 82, backgroundColor: 'white',
            marginHorizontal: 16,
            marginTop: 16,
            position: 'relative',
            borderRadius: 10,
            overflow: 'hidden',
        }}>

            <LinearGradient
                colors={['#90C8FB', '#8DC4F7E5', '#B6F5FE']}
                start={{ x: 0, y: 0.5 }}
                end={{ x: 1, y: 0.5 }}
                style={{
                    borderRadius: 10,
                    height: '100%',
                    width: '100%',
                    overflow: 'hidden',
                }}
            >
                <ImageBackground
                    source={require('../../assets/bg04.png')}
                    resizeMode="contain"
                    style={{
                        position: 'absolute',
                        bottom: -30,
                        left: -20,
                        width: 110,
                        height: 110,
                    }}
                />
                <View style={{ paddingLeft: 76, paddingTop: 16 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                        <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
                        <Text style={{ ...TypoSkin.heading7, color: ColorThemes.light.neutral_text_title_color }}>100$</Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                        <AppSvg SvgSrc={iconSvg.ruby} size={20} />
                        <Text style={{ ...TypoSkin.heading7, color: ColorThemes.light.neutral_text_title_color }}>

                            {customer?.TotalReward}
                        </Text>
                    </View>
                </View>
                <View style={{ position: 'absolute', right: 16, top: 25 }}>
                    <LinearGradient
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF']}
                        style={{ paddingHorizontal: 16, paddingVertical: 8, borderRadius: 8 }}>
                        <Text>
                            Đổi quà
                        </Text>
                    </LinearGradient>
                </View>
            </LinearGradient>
        </View>
    );
};

export default PointHome;
